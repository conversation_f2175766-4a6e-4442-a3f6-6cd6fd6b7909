// Global variables
let gameLevels = [];
let filteredGameLevels = [];
let gameContent = [];

// Pending changes tracking
let pendingChanges = new Map(); // questionId -> {originalData, modifiedData, isNew}
let editingQuestions = new Set(); // Set of questionIds currently being edited

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Initialize game content data
async function initializeGameData() {
    try {
        await loadGameContent();
        displayGameLevels();
    } catch (error) {
        console.error('Error initializing game data:', error);
        showNotification('Error loading game content from database', 'error');
    }
}

// Load game content from API
async function loadGameContent() {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
        const result = await response.json();

        if (result.success) {
            gameContent = result.data;
            processGameLevels();
        } else {
            throw new Error(result.error || 'Failed to load game content');
        }
    } catch (error) {
        console.error('Error loading game content:', error);
        showNotification(error.message || 'Error loading game content', 'error');
        gameContent = [];
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Process game content into levels
function processGameLevels() {
    const levelGroups = {};

    gameContent.forEach(content => {
        const levelNum = content.level_number;
        if (!levelGroups[levelNum]) {
            levelGroups[levelNum] = {
                levelID: levelNum,
                levelName: `Level ${levelNum}`,
                isActive: true,
                questions: []
            };
        }

        levelGroups[levelNum].questions.push({
            id: content.content_id,
            question: content.question_text,
            options: [content.option1, content.option2, content.option3, content.option4],
            correctAnswer: content.correct_answer
        });
    });

    gameLevels = Object.values(levelGroups).sort((a, b) => a.levelID - b.levelID);
    filteredGameLevels = [...gameLevels];

    if (gameLevels.length === 0) {
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Pending changes management functions
function addPendingChange(questionId, originalData, modifiedData) {
    pendingChanges.set(questionId, {
        originalData: { ...originalData },
        modifiedData: { ...modifiedData },
        isNew: false
    });
}

function removePendingChange(questionId) {
    pendingChanges.delete(questionId);
    editingQuestions.delete(questionId);
}

function hasPendingChanges(questionId) {
    return pendingChanges.has(questionId);
}

function getPendingChange(questionId) {
    return pendingChanges.get(questionId);
}

function getLevelPendingChanges(levelId) {
    const levelChanges = [];
    const level = gameLevels.find(l => l.levelID === levelId);
    if (level) {
        level.questions.forEach(question => {
            if (hasPendingChanges(question.id)) {
                levelChanges.push({
                    questionId: question.id,
                    change: getPendingChange(question.id)
                });
            }
        });
    }
    return levelChanges;
}

function clearAllPendingChanges() {
    pendingChanges.clear();
    editingQuestions.clear();
}

// Display game levels
function displayGameLevels() {
    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    if (filteredGameLevels.length === 0) {
        const isSearching = document.getElementById('levelSearch').value.trim() !== '';
        levelsContainer.innerHTML = `
            <div class="empty-levels-state">
                <div class="empty-icon">
                    <i class="fas fa-${isSearching ? 'search' : 'gamepad'}"></i>
                </div>
                <h3>${isSearching ? 'No Levels Found' : 'No Game Levels Found'}</h3>
                <p>${isSearching ? 'No levels match your search criteria. Try different keywords.' : 'No levels with questions have been created yet. Add some questions to create levels.'}</p>
                ${!isSearching ? '<button class="btn-primary" onclick="showAddQuestionModal(1)"><i class="fas fa-plus"></i> Add First Question</button>' : ''}
            </div>
        `;
        return;
    }

    filteredGameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });
}

// Create level card HTML
function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        const optionLabels = ['A', 'B', 'C', 'D'];
        const optionsHtml = question.options.map((option, index) => {
            const correctIndex = typeof question.correctAnswer === 'string'
                ? optionLabels.indexOf(question.correctAnswer.toUpperCase())
                : question.correctAnswer - 1;
            const isCorrect = index === correctIndex;
            return `<li>
                <span class="option-label">${optionLabels[index]}:</span>
                <span class="option-text">${option}</span>
                ${isCorrect ? '<span class="correct-answer">Correct</span>' : ''}
            </li>`;
        }).join('');

        // Check if this question has pending changes
        const hasPending = hasPendingChanges(question.id);
        const pendingClass = hasPending ? 'has-pending-changes' : '';
        const pendingIndicator = hasPending ? '<span class="pending-indicator" title="Unsaved changes">*</span>' : '';

        return `<div class="question-item ${pendingClass}" data-question-id="${question.id}">
            <div class="question-header">
                <div class="question-text">${question.question}${pendingIndicator}</div>
                <div class="question-actions">
                    ${hasPending ? `
                        <button class="btn-small btn-success" onclick="saveIndividualQuestion(${question.id})" title="Save this question">
                            <i class="fas fa-check"></i> OK
                        </button>
                        <button class="btn-small btn-secondary" onclick="revertQuestionChanges(${question.id})" title="Cancel changes">
                            <i class="fas fa-undo"></i>
                        </button>
                    ` : ''}
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="question-meta">
                <span>Level ${level.levelID}</span>
                ${hasPending ? '<span class="unsaved-label">Unsaved changes</span>' : ''}
            </div>
            <ul class="options-list">
                ${optionsHtml}
            </ul>
        </div>`;
    }).join('');

    // Count pending changes for this level
    const levelPendingChanges = getLevelPendingChanges(level.levelID);
    const hasPendingChanges = levelPendingChanges.length > 0;
    const pendingCountText = hasPendingChanges ? ` (${levelPendingChanges.length} unsaved)` : '';

    card.innerHTML = `
        <div class="level-header" onclick="toggleLevelDropdown(${level.levelID})">
            <div class="level-info">
                <div class="level-title-row">
                    <h3>${level.levelName}${pendingCountText}</h3>
                    <div class="level-meta">
                        <span class="questions-count">${level.questions.length} questions</span>
                        <span class="level-status ${level.isActive ? 'active' : 'inactive'}">
                            ${level.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            </div>
            <div class="level-actions" onclick="event.stopPropagation()">
                ${hasPendingChanges ? `
                    <button class="btn-primary btn-small" onclick="saveLevelChanges(${level.levelID})" title="Save all changes for this level">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                ` : ''}
                <button class="btn-secondary btn-small" onclick="showAddQuestionModal(${level.levelID})">
                    <i class="fas fa-plus"></i> Add Question
                </button>
            </div>
            <button class="dropdown-toggle" id="dropdown-toggle-${level.levelID}">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="level-questions collapsed ${level.questions.length === 0 ? 'empty' : ''}" id="level-questions-${level.levelID}">
            ${level.questions.length === 0 ?
                '<div class="empty-questions">No questions added yet. Click "Add Question" to get started.</div>' :
                questionsHtml
            }
        </div>
    `;

    return card;
}

// Search levels
function searchLevels() {
    const searchTerm = document.getElementById('levelSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredGameLevels = [...gameLevels];
    } else {
        filteredGameLevels = gameLevels.filter(level => {
            return level.levelID.toString().includes(searchTerm);
        });
    }

    displayGameLevels();
}

// Toggle level dropdown
function toggleLevelDropdown(levelID) {
    const questionsContainer = document.getElementById(`level-questions-${levelID}`);
    const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

    if (questionsContainer && dropdownToggle) {
        if (questionsContainer.classList.contains('collapsed')) {
            questionsContainer.classList.remove('collapsed');
            questionsContainer.classList.add('expanded');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        } else {
            questionsContainer.classList.remove('expanded');
            questionsContainer.classList.add('collapsed');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
        }
    }
}

// Toggle all levels
function toggleAllLevels() {
    const toggleButton = document.getElementById('toggleAllLevels');

    if (filteredGameLevels.length === 0) {
        return;
    }

    const isExpanding = toggleButton.textContent.includes('Expand');

    filteredGameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${level.levelID}`);

        if (questionsContainer && dropdownToggle) {
            if (isExpanding) {
                questionsContainer.classList.remove('collapsed');
                questionsContainer.classList.add('expanded');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
            } else {
                questionsContainer.classList.remove('expanded');
                questionsContainer.classList.add('collapsed');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
            }
        }
    });

    if (isExpanding) {
        toggleButton.innerHTML = '<i class="fas fa-compress-alt"></i> Collapse All';
    } else {
        toggleButton.innerHTML = '<i class="fas fa-expand-alt"></i> Expand All';
    }
}

// Show add question modal
function showAddQuestionModal(levelID) {
    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('levelNumber').value = levelID;
    document.getElementById('contentId').value = '';
    document.getElementById('questionForm').reset();
    modal.classList.add('show');
}

// Edit question - now supports batch editing mode
function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const question = level.questions.find(q => q.id === questionId);
    if (!question) return;

    // Store original data for potential revert
    const originalData = {
        id: questionId,
        question: question.question,
        options: [...question.options],
        correctAnswer: question.correctAnswer,
        levelID: levelID
    };

    // Check if there's already a pending change for this question
    let currentData = originalData;
    if (hasPendingChanges(questionId)) {
        currentData = getPendingChange(questionId).modifiedData;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('contentId').value = questionId;
    document.getElementById('questionText').value = currentData.question;
    document.getElementById('option1').value = currentData.options[0];
    document.getElementById('option2').value = currentData.options[1];
    document.getElementById('option3').value = currentData.options[2];
    document.getElementById('option4').value = currentData.options[3];

    // Handle both letter (A,B,C,D) and number (1,2,3,4) correct answers
    let correctAnswer = currentData.correctAnswer;
    if (typeof correctAnswer === 'string') {
        correctAnswer = ['A', 'B', 'C', 'D'].indexOf(correctAnswer.toUpperCase()) + 1;
    }
    document.getElementById('correctAnswer').value = correctAnswer;

    document.getElementById('levelNumber').value = levelID;

    // Store original data for this editing session
    modal.dataset.originalData = JSON.stringify(originalData);
    modal.dataset.questionId = questionId;
    modal.dataset.levelId = levelID;

    modal.classList.add('show');
}

// Handle add/edit question form submission - now supports batch editing
async function handleAddQuestion(event, levelID) {
    event.preventDefault();

    const questionText = document.getElementById('questionText').value;
    const option1 = document.getElementById('option1').value;
    const option2 = document.getElementById('option2').value;
    const option3 = document.getElementById('option3').value;
    const option4 = document.getElementById('option4').value;
    const correctAnswer = document.getElementById('correctAnswer').value;
    const levelNumber = document.getElementById('levelNumber').value;
    const contentId = document.getElementById('contentId').value;

    const isEdit = contentId !== '';

    if (isEdit) {
        // For edits, store changes locally instead of saving immediately
        handleEditQuestionLocal(event);
    } else {
        // For new questions, save immediately as before
        await handleAddQuestionImmediate(event, levelID);
    }
}

// Handle adding new questions (immediate save)
async function handleAddQuestionImmediate(event, levelID) {
    const questionText = document.getElementById('questionText').value;
    const option1 = document.getElementById('option1').value;
    const option2 = document.getElementById('option2').value;
    const option3 = document.getElementById('option3').value;
    const option4 = document.getElementById('option4').value;
    const correctAnswer = document.getElementById('correctAnswer').value;
    const levelNumber = document.getElementById('levelNumber').value;

    const formData = new FormData();
    formData.append('question_text', questionText);
    formData.append('option1', option1);
    formData.append('option2', option2);
    formData.append('option3', option3);
    formData.append('option4', option4);
    formData.append('correct_answer', correctAnswer);
    formData.append('level_number', levelNumber);

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
            method: 'POST',
            body: formData
        });
        const result = await response.json();

        if (result.success) {
            showNotification('Question added successfully', 'success');
            closeModal('questionModal');
            await loadGameContent(); // Refresh data
        } else {
            throw new Error(result.error || 'Failed to add question');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification(error.message || 'An error occurred', 'error');
    }
}

// Handle editing questions locally (no immediate save)
function handleEditQuestionLocal(event) {
    const modal = document.getElementById('questionModal');
    const questionId = parseInt(modal.dataset.questionId);
    const levelId = parseInt(modal.dataset.levelId);
    const originalData = JSON.parse(modal.dataset.originalData);

    const modifiedData = {
        id: questionId,
        question: document.getElementById('questionText').value,
        options: [
            document.getElementById('option1').value,
            document.getElementById('option2').value,
            document.getElementById('option3').value,
            document.getElementById('option4').value
        ],
        correctAnswer: parseInt(document.getElementById('correctAnswer').value),
        levelID: levelId
    };

    // Store the pending change
    addPendingChange(questionId, originalData, modifiedData);
    editingQuestions.add(questionId);

    // Update the display with modified data
    updateQuestionDisplay(questionId, modifiedData);

    closeModal('questionModal');
    showNotification('Changes saved locally. Use "OK" to save to database or "Save Changes" for batch save.', 'info');
}

// Update question display with modified data
function updateQuestionDisplay(questionId, modifiedData) {
    // Find and update the question in the gameLevels array
    const level = gameLevels.find(l => l.levelID === modifiedData.levelID);
    if (level) {
        const questionIndex = level.questions.findIndex(q => q.id === questionId);
        if (questionIndex !== -1) {
            level.questions[questionIndex] = {
                id: modifiedData.id,
                question: modifiedData.question,
                options: [...modifiedData.options],
                correctAnswer: modifiedData.correctAnswer
            };
        }
    }

    // Refresh the display
    displayGameLevels();
}

// Save individual question changes to database
async function saveIndividualQuestion(questionId) {
    const pendingChange = getPendingChange(questionId);
    if (!pendingChange) {
        showNotification('No changes to save for this question', 'warning');
        return;
    }

    const modifiedData = pendingChange.modifiedData;

    const formData = new FormData();
    formData.append('question_text', modifiedData.question);
    formData.append('option1', modifiedData.options[0]);
    formData.append('option2', modifiedData.options[1]);
    formData.append('option3', modifiedData.options[2]);
    formData.append('option4', modifiedData.options[3]);
    formData.append('correct_answer', modifiedData.correctAnswer);
    formData.append('level_number', modifiedData.levelID);

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=update_content&content_id=${questionId}`, {
            method: 'PUT',
            body: formData
        });
        const result = await response.json();

        if (result.success) {
            // Remove from pending changes
            removePendingChange(questionId);

            // Update the original data in gameContent array
            const contentIndex = gameContent.findIndex(c => c.content_id === questionId);
            if (contentIndex !== -1) {
                gameContent[contentIndex] = {
                    ...gameContent[contentIndex],
                    question_text: modifiedData.question,
                    option1: modifiedData.options[0],
                    option2: modifiedData.options[1],
                    option3: modifiedData.options[2],
                    option4: modifiedData.options[3],
                    correct_answer: modifiedData.correctAnswer,
                    level_number: modifiedData.levelID
                };
            }

            // Refresh display
            displayGameLevels();
            showNotification('Question saved successfully', 'success');
        } else {
            throw new Error(result.error || 'Failed to save question');
        }
    } catch (error) {
        console.error('Error saving question:', error);
        showNotification(error.message || 'Error saving question', 'error');
    }
}

// Revert question changes
function revertQuestionChanges(questionId) {
    const pendingChange = getPendingChange(questionId);
    if (!pendingChange) {
        return;
    }

    if (confirm('Are you sure you want to discard the changes to this question?')) {
        // Restore original data
        const originalData = pendingChange.originalData;
        updateQuestionDisplay(questionId, originalData);

        // Remove from pending changes
        removePendingChange(questionId);

        showNotification('Changes reverted successfully', 'info');
    }
}

// Save all pending changes for a specific level
async function saveLevelChanges(levelId) {
    const levelChanges = getLevelPendingChanges(levelId);

    if (levelChanges.length === 0) {
        showNotification('No changes to save for this level', 'warning');
        return;
    }

    // Show confirmation dialog with summary
    const changesSummary = levelChanges.map(change => {
        const modifiedData = change.change.modifiedData;
        return `• Question: "${modifiedData.question.substring(0, 50)}${modifiedData.question.length > 50 ? '...' : ''}"`;
    }).join('\n');

    const confirmMessage = `Save ${levelChanges.length} question(s) for Level ${levelId}?\n\nChanges to save:\n${changesSummary}`;

    if (!confirm(confirmMessage)) {
        return;
    }

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    // Save each question
    for (const change of levelChanges) {
        const questionId = change.questionId;
        const modifiedData = change.change.modifiedData;

        const formData = new FormData();
        formData.append('question_text', modifiedData.question);
        formData.append('option1', modifiedData.options[0]);
        formData.append('option2', modifiedData.options[1]);
        formData.append('option3', modifiedData.options[2]);
        formData.append('option4', modifiedData.options[3]);
        formData.append('correct_answer', modifiedData.correctAnswer);
        formData.append('level_number', modifiedData.levelID);

        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=update_content&content_id=${questionId}`, {
                method: 'PUT',
                body: formData
            });
            const result = await response.json();

            if (result.success) {
                // Remove from pending changes
                removePendingChange(questionId);

                // Update the original data in gameContent array
                const contentIndex = gameContent.findIndex(c => c.content_id === questionId);
                if (contentIndex !== -1) {
                    gameContent[contentIndex] = {
                        ...gameContent[contentIndex],
                        question_text: modifiedData.question,
                        option1: modifiedData.options[0],
                        option2: modifiedData.options[1],
                        option3: modifiedData.options[2],
                        option4: modifiedData.options[3],
                        correct_answer: modifiedData.correctAnswer,
                        level_number: modifiedData.levelID
                    };
                }

                successCount++;
            } else {
                errorCount++;
                errors.push(`Question ${questionId}: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            errorCount++;
            errors.push(`Question ${questionId}: ${error.message}`);
        }
    }

    // Refresh display
    displayGameLevels();

    // Show results
    if (errorCount === 0) {
        showNotification(`All ${successCount} questions saved successfully`, 'success');
    } else if (successCount > 0) {
        showNotification(`${successCount} questions saved, ${errorCount} failed. Check console for details.`, 'warning');
        console.error('Batch save errors:', errors);
    } else {
        showNotification(`Failed to save all questions. Check console for details.`, 'error');
        console.error('Batch save errors:', errors);
    }
}

// Additional change management functions
function hasAnyPendingChanges() {
    return pendingChanges.size > 0;
}

function getAllPendingChangesCount() {
    return pendingChanges.size;
}

function discardAllPendingChanges() {
    if (hasAnyPendingChanges()) {
        const count = getAllPendingChangesCount();
        if (confirm(`Discard all ${count} unsaved changes? This action cannot be undone.`)) {
            // Restore all original data
            for (const [questionId, change] of pendingChanges) {
                updateQuestionDisplay(questionId, change.originalData);
            }

            clearAllPendingChanges();
            showNotification(`${count} unsaved changes discarded`, 'info');
        }
    }
}

// Add warning when user tries to leave with unsaved changes
window.addEventListener('beforeunload', function(e) {
    if (hasAnyPendingChanges()) {
        const message = `You have ${getAllPendingChangesCount()} unsaved changes. Are you sure you want to leave?`;
        e.preventDefault();
        e.returnValue = message;
        return message;
    }
});

// Delete question
async function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question?')) {
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=delete_content&content_id=${questionId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                showNotification('Question deleted successfully', 'success');
                await loadGameContent(); // Refresh data
            } else {
                throw new Error(result.error || 'Failed to delete question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
        }
    }
}

// Close modal
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeGameData);

// Add form submit event listener
const questionForm = document.getElementById('questionForm');
if (questionForm) {
    questionForm.addEventListener('submit', handleAddQuestion);
}
